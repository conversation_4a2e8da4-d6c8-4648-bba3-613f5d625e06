/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: white;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: background 0.3s ease;
}

header.scrolled {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
}

header .flex {
    justify-content: space-between;
    align-items: center;
}

.logo img {
    height: 40px;
    transition: transform 0.3s ease;
}

.logo:hover img {
    transform: scale(1.05);
}

nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease, transform 0.3s ease;
}

nav a:hover {
    color: #e53935;
    transform: translateY(-2px);
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('image/hero1.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
    padding: 0 20px;
}

.hero h2 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    animation: fadeInUp 1s ease;
}

.hero p {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
    animation: fadeInUp 1s ease 0.2s both;
}

.hero .bg-red-600 {
    background: linear-gradient(135deg, #e53935 0%, #d32f2f 100%);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(229, 57, 53, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    animation: fadeInUp 1s ease 0.4s both;
}

.hero .bg-red-600:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(229, 57, 53, 0.4);
}

.hero input {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 5px;
    padding: 0.75rem;
    margin-bottom: 1rem;
    width: 100%;
    max-width: 300px;
    transition: background 0.3s ease;
}

.hero input:focus {
    outline: none;
    background: white;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.hero button {
    background: #1a1a1a;
    border: none;
    border-radius: 5px;
    padding: 0.75rem;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s ease;
}

.hero button:hover {
    background: #333;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Services Section */
section.py-16 {
    padding: 4rem 0;
}

section h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    text-align: center;
    color: #1a1a1a;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.service-card img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 1rem;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #e53935;
}

/* Why Us Section */
.why-us {
    background: linear-gradient(135deg, #e53935 0%, #d32f2f 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.why-us ul {
    list-style: none;
    max-width: 600px;
    margin: 0 auto;
}

.why-us li {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    padding-left: 1.5rem;
    position: relative;
}

.why-us li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #4caf50;
    font-weight: bold;
    font-size: 1.5rem;
}

/* Packages Section */
.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.package-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.package-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #e53935, #ff5722);
}

.package-card:hover {
    transform: scale(1.05);
}

.package-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #1a1a1a;
}

.package-card p {
    font-size: 2.5rem;
    font-weight: bold;
    color: #e53935;
    margin-bottom: 1rem;
}

.package-card ul {
    list-style: none;
    text-align: left;
}

.package-card li {
    margin-bottom: 0.5rem;
    color: #666;
}

/* Portfolio Section */
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.portfolio-grid img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.portfolio-grid img:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Testimonials Section */
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.testimonial-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
}

.testimonial-card img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 1rem;
}

.testimonial-card p:first-of-type {
    font-style: italic;
    margin-bottom: 1rem;
    color: #666;
}

.testimonial-card p:last-of-type {
    font-weight: bold;
    color: #1a1a1a;
}

/* Blog Section */
.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.blog-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-10px);
}

.blog-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.blog-card .content {
    padding: 1.5rem;
}

.blog-card h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: #1a1a1a;
}

.blog-card p {
    color: #666;
    margin-bottom: 1rem;
}

.blog-card button {
    background: linear-gradient(135deg, #e53935 0%, #d32f2f 100%);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.blog-card button:hover {
    opacity: 0.9;
}

/* 24/7 Section */
.services-24-7 {
    background: linear-gradient(135deg, #e53935 0%, #d32f2f 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.services-24-7 h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.services-24-7 p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.services-24-7 button {
    background: #1a1a1a;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s ease;
}

.services-24-7 button:hover {
    background: #333;
}

/* Contact Section */
.contact {
    padding: 4rem 0;
    text-align: center;
}

.contact-info {
    max-width: 500px;
    margin: 0 auto 2rem;
}

.contact-info p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.contact form {
    max-width: 500px;
    margin: 0 auto;
}

.contact input,
.contact textarea {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 1rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    transition: border-color 0.3s ease;
}

.contact input:focus,
.contact textarea:focus {
    outline: none;
    border-color: #e53935;
}

.contact button {
    background: linear-gradient(135deg, #e53935 0%, #d32f2f 100%);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    width: 100%;
    transition: opacity 0.3s ease;
}

.contact button:hover {
    opacity: 0.9;
}

/* Footer */
footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: white;
    padding: 3rem 0 1rem;
    text-align: center;
}

.footer-top {
    margin-bottom: 2rem;
}

.footer-top img {
    height: 40px;
    margin-bottom: 1rem;
}

.footer-top p {
    margin-bottom: 2rem;
    opacity: 0.8;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #e53935;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #e53935;
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero h2 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.2rem;
    }

    nav ul {
        gap: 1rem;
    }

    .services-grid,
    .packages-grid,
    .testimonials-grid,
    .blog-grid {
        grid-template-columns: 1fr;
    }

    .portfolio-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 480px) {
    .hero {
        height: 80vh;
        padding: 0 10px;
    }

    .hero h2 {
        font-size: 2rem;
    }

    header .flex {
        flex-direction: column;
        gap: 1rem;
    }

    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
}
