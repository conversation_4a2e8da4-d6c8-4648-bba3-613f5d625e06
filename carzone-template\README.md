# Carzone - Car Detailing & Auto Repair Website Template

A fully responsive, professional Bootstrap 5 website template for car detailing and auto repair businesses. This template features modern design, comprehensive functionality, and is built with semantic HTML5, custom SCSS, and vanilla JavaScript.

## 🚀 Features

- **Fully Responsive Design** - Works perfectly on all devices and screen sizes
- **Bootstrap 5.3.0** - Latest Bootstrap framework with modern components
- **Semantic HTML5** - Clean, accessible markup with proper ARIA attributes
- **Custom CSS/SCSS** - Professional styling with CSS custom properties
- **Vanilla JavaScript** - No jQuery dependency, pure JavaScript functionality
- **Font Awesome Icons** - Professional iconography throughout
- **Google Fonts** - Roboto font family for modern typography
- **SEO Optimized** - Proper meta tags and semantic structure
- **Performance Optimized** - Optimized images and efficient code
- **Cross-browser Compatible** - Works on all modern browsers

## 📱 Responsive Design

The template is built with a mobile-first approach and includes:
- Responsive navigation with mobile hamburger menu
- Flexible grid layouts that adapt to all screen sizes
- Optimized images with proper aspect ratios
- Touch-friendly interface elements
- Responsive typography and spacing

## 🎨 Design Elements

### Hero Section
- Full-width carousel slider with 3 slides
- Overlay content with call-to-action buttons
- Integrated booking form
- Smooth transitions and animations

### Service Cards
- Icon-based service presentation
- Hover effects and animations
- Responsive grid layout
- Professional styling

### Process Timeline
- Step-by-step process visualization
- Numbered icons with descriptions
- Responsive layout for mobile devices

### Gallery
- Filterable masonry layout
- Lightbox modal functionality
- Category-based filtering
- Smooth animations and transitions

### Pricing Tables
- Three-tier pricing structure
- Featured plan highlighting
- Responsive card layout
- Clear feature comparisons

### Testimonials
- Carousel-based testimonial display
- Customer photos and ratings
- Responsive design
- Smooth transitions

## 📄 Pages Included

### 1. Homepage (`index.html`)
**Complete homepage with all sections:**
- Hero slider with 3 slides and booking form
- Services section with 6 service cards
- About section with company information
- Process timeline (4 steps)
- Filterable gallery with lightbox modal
- Three-tier pricing section
- Testimonials carousel
- Blog preview section
- Contact form with Google Maps
- Footer with social links

### 2. Services Page (`services.html`)
**Comprehensive services showcase:**
- Page header with breadcrumbs
- 6 detailed service cards with features and pricing
- Why Choose Us section with 4 key benefits
- Service process timeline
- Call-to-action section
- Complete footer

### 3. Gallery Page (`gallery.html`)
**Professional portfolio showcase:**
- Filterable gallery with 6 categories
- 12 gallery items with overlay effects
- Lightbox modal for image viewing
- Responsive masonry layout
- Call-to-action section
- Complete footer

### 4. Contact Page (`contact.html`)
**Complete contact solution:**
- Contact information cards
- Professional contact form with validation
- FAQ accordion section
- Embedded Google Maps
- Call-to-action section
- Complete footer

## 🛠️ Technical Specifications

### HTML5 Features
- Semantic markup (`<header>`, `<nav>`, `<main>`, `<section>`, `<article>`, `<footer>`)
- ARIA attributes for accessibility
- Proper heading hierarchy
- Form validation attributes
- Meta tags for SEO

### CSS Features
- CSS Custom Properties (CSS Variables)
- Flexbox and CSS Grid layouts
- CSS animations and transitions
- Media queries for responsive design
- Modern CSS techniques

### JavaScript Features
- ES6+ syntax
- Intersection Observer API for scroll animations
- Form validation and submission
- Gallery filtering and lightbox
- Smooth scrolling navigation
- Back-to-top functionality

## 📁 File Structure

```
carzone-template/
├── index.html              # Homepage
├── services.html           # Services page
├── gallery.html           # Gallery page
├── contact.html           # Contact page
├── assets/
│   ├── css/
│   │   └── style.css      # Custom styles
│   ├── js/
│   │   └── main.js        # Custom JavaScript
│   └── images/            # Image assets
│       ├── hero1.jpg      # Hero slider images
│       ├── hero2.jpg
│       ├── hero3.jpg
│       ├── wash.jpg       # Service images
│       ├── ceramic.jpg
│       └── logo.png       # Logo
└── README.md              # This file
```

## 🚀 Getting Started

1. **Download/Clone** the template files
2. **Open** `index.html` in your web browser
3. **Customize** content, images, and styling as needed
4. **Deploy** to your web server or hosting platform

## 🎯 Customization

### Colors
The template uses CSS custom properties for easy color customization:
```css
:root {
  --primary-color: #e53935;    /* Red primary color */
  --secondary-color: #1a1a1a;  /* Dark secondary color */
  --accent-color: #ff6b35;     /* Orange accent color */
  --white: #ffffff;
  --light-gray: #f8f9fa;
  --dark-gray: #6c757d;
}
```

### Typography
- Primary font: Roboto (Google Fonts)
- Font weights: 300, 400, 500, 700, 900
- Responsive typography with rem units

### Images
Replace images in the `assets/images/` directory:
- Hero images: 1920x1080px recommended
- Service icons: Use Font Awesome or custom SVGs
- Gallery images: 800x600px recommended
- Logo: PNG format with transparency

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+ (with polyfills)

## 🔧 Dependencies

### External CDN Resources
- Bootstrap 5.3.0 CSS & JS
- Font Awesome 6.4.0 Icons
- Google Fonts (Roboto)

### No Additional Dependencies
- No jQuery required
- No additional JavaScript libraries
- Pure CSS animations
- Vanilla JavaScript functionality

## 📈 Performance Features

- Optimized images with proper compression
- Minified CSS and JavaScript (production ready)
- Lazy loading for images
- Efficient CSS with minimal unused styles
- Fast loading times

## ♿ Accessibility Features

- ARIA labels and attributes
- Semantic HTML structure
- Keyboard navigation support
- Screen reader friendly
- High contrast ratios
- Focus indicators

## 📞 Support & Customization

This template is designed to be easily customizable for your specific business needs. Key areas for customization:

1. **Content**: Update all text content with your business information
2. **Images**: Replace with your own high-quality photos
3. **Colors**: Modify CSS custom properties to match your brand
4. **Services**: Update service offerings and pricing
5. **Contact Info**: Update all contact information and location

## 🌟 Features Highlights

- **Mobile-First Design**: Optimized for mobile devices first
- **SEO Ready**: Proper meta tags and semantic structure
- **Fast Loading**: Optimized for performance
- **Easy to Customize**: Well-organized code structure
- **Professional Design**: Modern and clean aesthetic
- **Cross-Browser**: Works on all modern browsers
- **Accessibility**: WCAG compliant design
- **No Dependencies**: Pure HTML, CSS, and JavaScript

## 📝 License

This template is provided as-is for educational and commercial use. Feel free to modify and customize according to your needs.

---

**Carzone Template** - Professional Car Detailing & Auto Repair Website Template
Built with ❤️ using Bootstrap 5, HTML5, CSS3, and Vanilla JavaScript
